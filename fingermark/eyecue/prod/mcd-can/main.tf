module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "common" {
  source            = "../../../../modules/common"
  aws_account_id    = data.aws_caller_identity.current.account_id
  aws_region        = var.AWS_REGION
  client_name       = var.CLIENT_NAME
  client_acronym    = var.CLIENT_ACRONYM
  country           = var.COUNTRY
  country_full      = var.COUNTRY_FULL
  aws_iam_roles     = ["AdminAccess", "DevAccess", "PowerAccess", "DeployerAccess", "DataScientist", "QuicksightAdminAccess"]
  keybase           = var.KEYBASE
  cloudcraft_access = true
  env               = var.ENVIRONMENT
}

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}


module "vanta" {
  source = "../../../../modules/vanta"
}


resource "aws_key_pair" "infra-keypair" {
  key_name   = "infra-keypair"
  public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABgQDH1O316YL1/kn2n/s5nmbA3B8ELexJcIhScJRmnlcnbfPuMK2nqUNtIfx9JmiZx8F+NBFrzKZxa3IqzSP8FsDk7auL5XFcxSrF0NlUzY0JuqjwNHZJaYM0bqxVVlBl2Qq+/Sm26WQZ/K0X8lyMl9yOjVMm+SkfaIPWPANnrhBwlmrQRsgnuPlyX0iMpVKi1BRU+gfMbWJZxp0n6/jfQ1YfVu4FJpvxxrWpfqo/94N/cU9A0gm5YoSiF6mx1Fv4V08zVtBKDl/Fevu2g7TEuFJLSTbs2ZRYBG4hlr0KH+R3i+OuutYAs9XkqIt5C1lfjOIWyrFuJ6W0C1H02EdT9dMek/IHUWYBXYYQ51yDWutkuxoL7uZJAwq2tktbWnCRwOKI3F13QyBniiUxMqjmnu9rlaX8SXwfTrEP9jM1zKFkqm1tEnRvpJTqaG1Arzb1JVtEsTZZYJiXeI0Z7CtyNAiYTeB5kXUdI+2odfesSmXCOPOMfSZ9aN9y1/vtEGz4tYU= <EMAIL>"
  tags       = var.default_tags
}


module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags)
}


module "secret_manager" {
  source                              = "../../../../modules/secret_manager"
  eyecue_postgres_lambdas_secret_name = "rds/ssm/eyecue-postgres-lambdas"
  eyecue_dashboard_data_secret_name   = "rds/ssm/eyecue-dashboard-data"
}


module "icinga2_satellite" {
  source                                         = "../../../../modules/icinga2_satellite"
  icinga2_satellite_vpc_id                       = module.eyecue_network.vpc_id
  icinga2_satellite_ec2_ami_id                   = "ami-02da4d5de61d161c5"
  icinga2_satellite_ec2_subnet_id                = module.eyecue_network.public_subnet_ids[0]
  icinga2_satellite_ec2_extra_security_group_ids = [module.eyecue_network.havelock_security_group_id]
  icinga2_satellite_customer_id                  = var.CLIENT_ACRONYM
  icinga2_satellite_cloudflare_api_key           = data.vault_generic_secret.cloudflare.data["api_key"]
  icinga2_satellite_ec2_ssh_key_name             = aws_key_pair.infra-keypair.key_name
}


module "eyecue_mimir" {
  source                   = "../../../../modules/eyecue_mimir"
  mimir_aws_account_id     = "************"
  lambda_role_arn_suffixes = var.lambda_role_arn_suffixes
  policy_description       = "Policy to invoke request-roisuggestor-png lambda from cross accounts."
  AWS_ACCOUNT_ID           = data.aws_caller_identity.current.account_id
  AWS_REGION               = var.AWS_REGION
}


module "eyecue_iot_kinesis_eventstream" {
  source                         = "../../../../modules/eyecue_iot_kinesis_eventstream"
  client_acronym                 = var.CLIENT_ACRONYM
  kinesis_iot_topic_rules_config = var.kinesis_iot_topic_rules_config
  fallback_bucket_name           = "fm-data-eyecue-kinesis-failure-ca-central-1"
  enable_fallback_to_s3          = true
}

module "eyecue_rds" {
  source                        = "../../../../modules/eyecue_rds"
  create_replica                = false
  rds_apply_changes_immediately = true
  rds_master_instance_class     = "db.t3.medium" #"db.t3.large"
  rds_engine_version            = "16.8"
  rds_ca_cert_identifier        = "rds-ca-rsa2048-g1"
  special_password              = false
  aws_region                    = var.AWS_REGION
  aws_account_id                = data.aws_caller_identity.current.account_id
  product                       = "Eyecue"
  vpc_id                        = module.eyecue_network.vpc_id
  subnet_ids                    = module.eyecue_network.public_subnet_ids
  rds_allocated_storage         = 400
  rds_max_allocated_storage     = 1000
  rds_master_iops               = 12000
  rds_replica_iops              = 12000
  eyecue_rds_cloudflare_api_key = data.vault_generic_secret.cloudflare.data["api_key"]
  eyecue_rds_customer_id        = var.CLIENT_ACRONYM
  create_db_parameter_group     = true
  parameter_group_family        = "postgres16"
  parameter_group_parameters = [ # Explicitly disable forced SSL
    {
      name  = "rds.force_ssl"
      value = "0"
    }
  ]
  allow_major_version_upgrade = true
}

module "kinesis_aggregated" {
  source                             = "../../../../modules/kinesis_data_stream"
  aws_region                         = var.AWS_REGION
  client_name                        = var.CLIENT_ACRONYM
  redshift_aws_account_ids_roles     = var.redshift_aws_account_ids_roles
  retention_period                   = var.kinesis_data_stream_retention_period
  stream_mode                        = var.kinesis_data_stream_stream_mode
  stream_name_list                   = ["aggregate"]
  current_account_id                 = data.aws_caller_identity.current.account_id
  create_role                        = true
  is_data_sharing_enabled            = false
  redshift_stream_access_policy_name = "kinesis_stream_policy"
}

module "kinesis_eventstream" {
  source                         = "../../../../modules/kinesis_data_stream"
  aws_region                     = var.AWS_REGION
  client_name                    = var.CLIENT_ACRONYM
  redshift_aws_account_ids_roles = var.redshift_aws_account_ids_roles
  retention_period               = var.kinesis_data_stream_retention_period
  stream_mode                    = var.kinesis_data_stream_stream_mode
  stream_name_list               = ["eventstream"]
  current_account_id             = data.aws_caller_identity.current.account_id
  create_role                    = false
}

module "aws_config_recorder" {
  providers = {
    aws = aws.ap-southeast-2
  }

  source = "../../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags         = var.tags
  default_tags = var.default_tags
}

# ------ CloudWatch Log Retention Management ------

module "cw_log_retention_ca_central_1" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags

  providers = {
    aws = aws.ap-southeast-2
  }
}

# ===============================================
# CloudWatch Alarms
# ===============================================
locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
      ca_central_1 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ca_central_1,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ca_central_1
        )
      }
    }
  }
}

module "rds_cw_alarms" {
  source                                 = "../../../../modules/rds_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ca-central-1"]]
  cw_alarm_config_rds_cpu_util           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_mem_free           = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_disk_queue_depth   = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_write_iops         = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_read_iops          = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  cw_alarm_config_rds_free_storage_space = { (module.eyecue_rds.master_db_instance_name) = { identifier = module.eyecue_rds.master_db_instance_identifier } }
  tags                                   = var.tags
  default_tags                           = var.default_tags
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  providers = {
    aws = aws.ap-southeast-2
  }
  source                                 = "../../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

module "dynamodb_cw_alarms_defaults_ca_central_1" {
  source                                 = "../../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ca-central-1"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ca_central_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ca_central_1.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ca_central_1.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ca_central_1.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

module "ec2_instance_cw_alarms_ca_central_1" {
  # providers      = { aws = aws.ca-central-1 } # DEFAULT AWS PROVIDER: ca-central-1
  source         = "../../../../modules/ec2_instance_cw_alarms"
  sns_topic_arns = [var.cw_alarms_sns_topic_arns_region_lookup["ca-central-1"]]
  cw_alarm_config_ec2_by_tags_cpu_util_high = {
    "icinga2-satellite" = { instance_tags = { Name = "icinga2-satellite" } }
  }
  cw_alarm_config_ec2_by_tags_cpu_util_low = {}
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ca_central_1" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ca-central-1"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Production"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
