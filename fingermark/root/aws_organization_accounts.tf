# ==========================================
# Security Accounts
# ==========================================
resource "aws_organizations_account" "log_archive" {
  name      = "Log Archive"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.security.id
}
resource "aws_organizations_account" "audit" {
  name      = "Audit"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.security.id
}

# ==========================================
# Supersonic Accounts
# ==========================================
resource "aws_organizations_account" "supersonic_prod" {
  name      = "Supersonic Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.supersonic_prod.id
}
resource "aws_organizations_account" "supersonic_americana_prod" {
  name      = "Supersonic Americana Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.supersonic_prod.id
}
resource "aws_organizations_account" "supersonic_rbnz_prod" {
  name      = "Supersonic RBNZ Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.supersonic_prod.id
}

# ==========================================
# Support Accounts
# ==========================================
resource "aws_organizations_account" "fingermark_support_production" {
  name      = "Fingermark Support Production"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.support.id
}

# ==========================================
# Northvue Accounts
# ==========================================
resource "aws_organizations_account" "northvue_dev" {
  name      = "Northvue Dev"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.northvue_dev.id
}
resource "aws_organizations_account" "northvue_staging" {
  name      = "Northvue Staging"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.northvue_stg.id
}
resource "aws_organizations_account" "northvue_prod" {
  name      = "Northvue Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.northvue_prod.id
}

# ==========================================
# Samsung DMBO Accounts
# ==========================================
resource "aws_organizations_account" "samsung_dmbo_prod" {
  name      = "Samsung DMBO Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.samsung_dmbo_prod.id
}

# ==========================================
# Data Accounts
# ==========================================
resource "aws_organizations_account" "data_prod" {
  name      = "Data Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.data_prod.id
}
resource "aws_organizations_account" "data_dev" {
  name      = "Data Dev"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.data_dev.id
}

# ==========================================
# Eyecue DEV Accounts
# ==========================================
resource "aws_organizations_account" "eyecue_cv_dev" {
  name      = "Eyecue CV Dev"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}
resource "aws_organizations_account" "eyecue_cv_staging_customer" {
  name      = "Eyecue CV Staging (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}
resource "aws_organizations_account" "eyecue_qa_aus" {
  name      = "Eyecue qa-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}
resource "aws_organizations_account" "eyecue_cv_staging" {
  name      = "Eyecue CV Staging"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}
resource "aws_organizations_account" "eyecue_dev_nzl" {
  name      = "Eyecue Development NZL"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}
resource "aws_organizations_account" "eyecue_stg_nzl" {
  name      = "Eyecue Staging NZL"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.eyecue_dev.id
}

# ==========================================
# Eyecue PROD Customer Accounts
# ==========================================
resource "aws_organizations_account" "eyecue_cul_usa" {
  name      = "Eyecue cul-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_czp_usa" {
  name      = "Eyecue czp-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_mcd_can" {
  name      = "Eyecue mcd-can (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_cfa_usa" {
  name      = "Eyecue cfa-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_poc_aus" {
  name      = "Eyecue poc-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_kfc_aus" {
  name      = "Eyecue kfc-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_bkg_usa" {
  name      = "Eyecue bkg-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_pop_nzl" {
  name      = "Eyecue pop-nzl (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_stb_nzl" {
  name      = "Eyecue stb-nzl (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_elj_aus" {
  name      = "Eyecue elj-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_mcd_nzl" {
  name      = "Eyecue mcd-nzl (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_mcd_aus" {
  name      = "Eyecue mcd-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_stb_usa" {
  name      = "Eyecue stb-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_pop_usa" {
  name      = "Eyecue pop-usa (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_bkg_nzl" {
  name      = "Eyecue bkg-nzl (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_zmb_aus" {
  name      = "Eyecue zmb-aus (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}
resource "aws_organizations_account" "eyecue_tim_can" {
  name      = "Eyecue tim-can (customer)"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.customer_accounts.id
}

# ==========================================
# Eyecue PROD Internal
# ==========================================
resource "aws_organizations_account" "eyecue_cv_prod" {
  name      = "Eyecue CV Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.internal_accounts.id
}

# ==========================================
# Platform Accounts
# ==========================================
resource "aws_organizations_account" "infra_prod" {
  name      = "Infra Prod"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.platform.id
}
resource "aws_organizations_account" "fingermark_users" {
  name      = "Fingermark Users"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.platform.id
}
resource "aws_organizations_account" "infra_dev" {
  name      = "Infra Dev"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.platform.id
}

# ==========================================
# FM Production Account
# ==========================================
resource "aws_organizations_account" "fingermark_production" {
  name      = "Fingermark Production"
  email     = "<EMAIL>"
  parent_id = aws_organizations_organizational_unit.fm_prod.id
}
