module "iam_password_policy" {
  source = "../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "SupportAccess", "ICXeedAccess"]
}

module "vanta" {
  source = "../../modules/vanta"
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# The SAML provider (IAM IdP) resources were manually created in the AWS console using Jumpcloud's metadata
module "jumpcloud_aws_connect" {
  source = "../../modules/jumpcloud_aws_connect"

  # Development environment
  dev_saml_provider_arn = "arn:aws:iam::************:saml-provider/Jumpcloud_Connect_DONTDELETE"
  dev_connect_instances = [
    "arn:aws:connect:ap-southeast-2:************:instance/8bed3770-6d92-4239-a303-7188cc0b57b7/user/$${aws:userid}"
  ]

  # Production environment
  prod_saml_provider_arn = "arn:aws:iam::************:saml-provider/Jumpcloud_Connect_Prod_DONTDELETE"
  prod_connect_instances = [
    "arn:aws:connect:ap-southeast-2:************:instance/b597ca97-699a-4e73-8d13-b91602f45f45/user/$${aws:userid}"
  ]
}

module "vpc_flow_logs_ap_southeast_2" {
  providers = {
    aws = aws
  }
  source          = "../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "vpc_flow_logs_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source          = "../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = merge(var.default_tags, var.tags)
}

module "aws_config_recorder" {
  source = "../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags = {
    Environment = "Production"
    Squad       = "Platfrorm team"
    Application = "IAM-Monitoring"
  }
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

# ===============================================
# CloudWatch Alarms
# ===============================================

locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
    }
  }
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  source                                 = "../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  tags                                   = var.tags
  default_tags                           = var.default_tags
}
