image: hashicorp/terraform:1.7.2

pipelines:
  custom: # Pipeline that is triggered manually
    terraform-pipeline:
      - variables:
          - name: Environment
            default: "fingermark_environment_here"
            allowed-values:
              - "fingermark_environment_here"
              - "_deprecated/cms"
              - "_deprecated/fm-dev"
              - "_deprecated/northvue/dev"
              - "_deprecated/northvue/prod"
              - "_deprecated/northvue/stg"
              - "_deprecated/qa"
              - "_deprecated/uat"
              - "dmbo/prod"
              - "eyecue/dev/cvland"
              - "eyecue/dev/dev-nzl"
              - "eyecue/dev/poc"
              - "eyecue/prod/bkg-nzl"
              - "eyecue/prod/bkg-usa"
              - "eyecue/prod/cfa-usa"
              - "eyecue/prod/cul-usa"
              - "eyecue/prod/cvland"
              - "eyecue/prod/czp-usa"
              - "eyecue/prod/elj-aus"
              - "eyecue/prod/kfc-aus"
              - "eyecue/prod/mcd-aus"
              - "eyecue/prod/mcd-can"
              - "eyecue/prod/mcd-nzl"
              - "eyecue/prod/pop-nzl"
              - "eyecue/prod/pop-usa"
              - "eyecue/prod/ptl-usa"
              - "eyecue/prod/stb-nzl"
              - "eyecue/prod/stb-usa"
              - "eyecue/prod/tim-can"
              - "eyecue/prod/zmb-aus"
              - "eyecue/stg/cvland"
              - "eyecue/stg/stg-nzl"
              - "infra/dev"
              - "infra/prod"
              - "root"
              - "supersonic/americana"
              - "supersonic/s2-rbnz"
              - "supersonic/supersonic-prod"
              - "support"
      - step:
          name: terraform-plan
          script:
            - cd fingermark/$Environment
            - terraform init
            - terraform validate
            - terraform plan -out=$BITBUCKET_CLONE_DIR/tfplan
          artifacts:
            - tfplan
      # - step:
      #     name: terraform-apply
      #     trigger: manual
      #     script:
      #       - |
      #         if [ "$BITBUCKET_BRANCH" != "master" ]; then
      #           echo "Error: This pipeline can only be run from the master branch."
      #           exit 1
      #         fi
      #       - cd fingermark/$Environment
      #       - terraform init
      #       - terraform apply -auto-approve $BITBUCKET_CLONE_DIR/tfplan