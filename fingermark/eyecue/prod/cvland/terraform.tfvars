AWS_REGION     = "ap-southeast-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "cvland-prod"
CLIENT_ACRONYM = "cvland-prod"
COUNTRY        = "au"
ENVIRONMENT    = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_ap_southeast_2 = {
  "auto_provisioner_api_logs"                                            = { table_name = "auto_provisioner_api_logs" },
  "custom-dayparts-and-shifts"                                           = { table_name = "custom-dayparts-and-shifts" },
  "dashboard-site-configuration"                                         = { table_name = "dashboard-site-configuration" },
  "databoard-report-bull-jobs"                                           = { table_name = "databoard-report-bull-jobs" },
  "databoard-report-users"                                               = { table_name = "databoard-report-users" },
  "databoard-site-configs"                                               = { table_name = "databoard-site-configs" },
  "databoard_logs"                                                       = { table_name = "databoard_logs" },
  "databoard_sites_configuration"                                        = { table_name = "databoard_sites_configuration" },
  "databoard_users"                                                      = { table_name = "databoard_users" },
  "eyecue-dashboard-cameras"                                             = { table_name = "eyecue-dashboard-cameras" },
  "eyecue-dashboard-clients"                                             = { table_name = "eyecue-dashboard-clients" },
  "eyecue-deployment-params-template"                                    = { table_name = "eyecue-deployment-params-template" },
  "eyecue-inventory-table"                                               = { table_name = "eyecue-inventory-table" },
  "eyecue-machine-containers"                                            = { table_name = "eyecue-machine-containers" },
  "eyecue-provisioning-log"                                              = { table_name = "eyecue-provisioning-log" },
  "eyecue-provisioning-table"                                            = { table_name = "eyecue-provisioning-table" },
  "eyecue-scheduled-validation"                                          = { table_name = "eyecue-scheduled-validation" },
  "eyecue-serverless-pipeline-deployment-register"                       = { table_name = "eyecue-serverless-pipeline-deployment-register" },
  "eyecue-store-central-accounts-prod"                                   = { table_name = "eyecue-store-central-accounts-prod" },
  "eyecue-store-central-servers-prod"                                    = { table_name = "eyecue-store-central-servers-prod" },
  "eyecue-trigger-execution-history"                                     = { table_name = "eyecue-trigger-execution-history" },
  "eyecue-trigger-service"                                               = { table_name = "eyecue-trigger-service" },
  "eyecue-tunnel-table"                                                  = { table_name = "eyecue-tunnel-table" },
  "eyecue-weights-template"                                              = { table_name = "eyecue-weights-template" },
  "m2m-token-cache-beta"                                                 = { table_name = "m2m-token-cache-beta" },
  "m2m-token-cache-prod"                                                 = { table_name = "m2m-token-cache-prod" },
  "serverless-graphql-rds-cvlandprod-databoardReportUsers-1652XID7H1FU0" = { table_name = "serverless-graphql-rds-cvlandprod-databoardReportUsers-1652XID7H1FU0" },
  "serverless-graphql-rds-cvlandprod-databoardSiteConfigs-2NSLAM3HV7XO"  = { table_name = "serverless-graphql-rds-cvlandprod-databoardSiteConfigs-2NSLAM3HV7XO" },
  "sso-tokens"                                                           = { table_name = "sso-tokens" },
  "Site"                                                                 = { table_name = "Site" },
  "ratatoskr-chromebox-configuration"                                    = { table_name = "ratatoskr-chromebox-configuration" },
  "ratatoskr-dashboard-client-specific-configurations"                   = { table_name = "ratatoskr-dashboard-client-specific-configurations" },
  "ratatoskr-dashboard-configuration"                                    = { table_name = "ratatoskr-dashboard-configuration" },
  "terraform-state"                                                      = { table_name = "terraform-state" },
}
dynamodb_cw_alarms_defaults_gsis_ap_southeast_2 = {
  "eyecue-trigger-execution-history-triggerIdIndex" = { table_name = "eyecue-trigger-execution-history", index_name = "triggerIdIndex" },
  "Site-GSIDeploymentDate"                          = { table_name = "Site", index_name = "GSIDeploymentDate" },
  "Site-GSIFingermarkID"                            = { table_name = "Site", index_name = "GSIFingermarkID" },
}
dynamodb_cw_alarms_defaults_tables_us_east_1 = {
  "ratatoskr-dashboard-configuration" = { table_name = "ratatoskr-dashboard-configuration" }
}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
