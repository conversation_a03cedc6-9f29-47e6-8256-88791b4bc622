
variable "AWS_REGION" {
  default = "ap-southeast-2"
}

variable "trusted_aws_account_id" {
  description = "This should be the aws account ID of the staff account"
  default     = "************"
}

### NETWORK Module ###

variable "vpc_cidr_block" {
  description = "AWS VPC cidr block"
  type        = string
  default     = "**********/16"
}

variable "vpc_cidr_block_us_east_1" {
  description = "AWS VPC CIDR block for the US region"
  type        = string
  default     = "10.0.0.0/16"
}

variable "public_subnets" {
  description = "Infrastructure - Network Public Subnets List"
  type        = list(any)
  default     = ["**********/20", "***********/20", "***********/20"]
}

variable "public_subnets_us_east_1" {
  description = "List of public subnet CIDRs for the US region"
  type        = list(string)
  default     = ["10.0.0.0/20", "*********/20", "*********/20"]
}

variable "vpc_azs" {
  description = "Infrastructure - Network Availability Zones"
  type        = list(any)
  default     = ["ap-southeast-2a", "ap-southeast-2c", "ap-southeast-2b"]
}

variable "vpc_azs_us_east_1" {
  description = "Availability zones for the US region"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "private_subnets" {
  description = "Infrastructure - Network Private Subnets List"
  type        = list(any)
  default     = ["***********/20", "***********/20", "***********/20"]
}

variable "private_subnets_us_east_1" {
  description = "List of private subnet CIDRs for the US region"
  type        = list(string)
  default     = ["*********/20", "*********/20", "*********/20"]
}

variable "tags" {
  description = "Infrastructure Tags"
  type        = map(any)
  default     = {}
}

variable "default_tags" {
  description = "Infrastructure Default Tags"
  type        = map(any)
  default = {
    Terraform   = "True"
    Squad       = "Platform"
    Environment = "Production"
    Product     = "Eyecue"
  }
}

variable "customer" {
  default     = "fingermark"
  description = "Fingermark Customer Name"
  type        = string
}

variable "env" {
  default     = "prod"
  description = "Fingermark Environment"
  type        = string
}

variable "product" {
  default     = "infra"
  description = "Fingermark Product"
  type        = string
}

### K8s Module ###
variable "k8s_cluster_name" {
  description = "Kubernetes Cluster Name"
  default     = "fingermark-infra-k8s-cluster"
}

variable "k8s_cluster_version" {
  description = "Kubernetes Cluster version"
  default     = "1.30"
}

variable "k8s_cluster_keypair" {
  description = "Kubernetes Cluster Keypair"
  default     = "fm-infra-team"
}

variable "k8s_cluster_instancetype" {
  description = "Kubernetes Cluster Instance Type"
  default     = "t3.medium"
}

variable "k8s_cluster_log_types" {
  description = "Kubernetes Cluster Log Type"
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "k8s_access" {
  description = "Kubernetes Additional Access"
  default     = ["***************/32", "***************/32", "**************/32", "*************/32", "**************/32", "**************/32"]
}

variable "bottlerocket_version" {
  description = "Kubernetes Cluster AMI"
  default     = "1.20.0"
}

variable "k8s_asg_maxsize" {
  description = "Kubernetes Cluster - ASG Max Size"
  default     = "8"
}

variable "k8s_on_demand_percentage_above_base_capacity" {
  description = "Kubernetes Cluster - On-Demand Percentage Above Base Capacity"
  default     = "10"
}

variable "k8s_on_demand_base_capacity" {
  description = "Kubernetes Cluster - On-Demand Base Capacity"
  default     = "1"
}

variable "ecr_account_region" {
  description = "ECR Account Region"
  default     = "ap-southeast-2"
}

variable "ecr_account_id" {
  description = "ECR Account ID"
  default     = "************"
}

variable "additional_routes" {
  description = "A map of CIDR blocks to ENI IDs for AWX"
  type        = map(string)
  default = {
    "*************/24" = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
    "**********/22"    = "eni-01afc64f3aecafb46"
    "*********/22"     = "eni-01afc64f3aecafb46"
  }
}

variable "statuspage_custom_domain" {
  description = "Custom domain to be used for the Atlassian status page"
  type        = string
  default     = "status.fingermark.tech"
}

variable "atlassian_statuspage_target" {
  description = "The Atlassian-provided target hostname for the custom domain (e.g. custom.atlassianstatuspage.io)"
  type        = string
  default     = "fmcvb5fd17fx.stspg-customer.com"
}

variable "victoria_metrics_us_east_1_alb_domain_name" {
  description = "Custom domain to be used for the Atlassian status page"
  type        = string
  default     = "victoria-metrics.us-east-1.fingermark.tech"
}

variable "victoria_metrics_ap_southeast_2_alb_domain_name" {
  description = "Custom domain to be used for the Atlassian status page"
  type        = string
  default     = "victoria-metrics.ap-southeast-2.fingermark.tech"
}

variable "alarm_slack_webhook_url" {
  description = "The Slack webhook URL to use for CloudWatch alarms."
  type        = string
  default     = "*******************************************************************************"
}

variable "alarm_slack_channel" {
  description = "The Slack channel to use for CloudWatch alarms."
  type        = string
  default     = "#infra-alerts"
}

variable "alarm_slack_username" {
  description = "The Slack username to use for CloudWatch alarms."
  type        = string
  default     = "Infra"
}

# ===============================================
# CloudWatch Alarms
# ===============================================
variable "cw_alarms_sns_topic_arns_region_lookup" {
  description = <<-DOC
    Map of region names to SNS topic ARNs for CloudWatch alarm notifications. The SNS topic ARNs
    should have already been created in a centralised AWS account (infra #************) using the
    module `modules/cw_alarm_notifications_sns_topic`.
  DOC
  type        = map(string)
  default = {
    "ap-southeast-2" = "arn:aws:sns:ap-southeast-2:************:cloudwatch-alarms-org"
    "ca-central-1"   = "arn:aws:sns:ca-central-1:************:cloudwatch-alarms-org"
    "us-east-1"      = "arn:aws:sns:us-east-1:************:cloudwatch-alarms-org"
    "us-west-1"      = "arn:aws:sns:us-west-1:************:cloudwatch-alarms-org"
    "us-west-2"      = "arn:aws:sns:us-west-2:************:cloudwatch-alarms-org"
  }
}

variable "dynamodb_cw_alarms_defaults_tables_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB tables in ap-southeast-2 region to configure CloudWatch alarms with
    default thresholds. This is intended for use with "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
  }))
  default = {}
}

variable "dynamodb_cw_alarms_defaults_gsis_ap_southeast_2" {
  description = <<-DOC
    Map of additional DynamoDB global secondary indexes in ap-southeast-2 region to configure
    CloudWatch alarms with default thresholds. This is intended for use with
    "modules/dynamodb_cw_alarms".
  DOC
  type = map(object({
    table_name : string
    index_name : string
  }))
  default = {}
}
