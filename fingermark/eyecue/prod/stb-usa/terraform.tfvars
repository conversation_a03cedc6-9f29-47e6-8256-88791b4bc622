
AWS_REGION     = "us-west-2"
KEYBASE        = "keybase:fingermark"
CLIENT_NAME    = "stb"
CLIENT_ACRONYM = "stb"
COUNTRY        = "us"
ENVIRONMENT    = "prod"

# ===============================================
# CloudWatch Alarms
# ===============================================
dynamodb_cw_alarms_defaults_tables_us_east_1 = {}
dynamodb_cw_alarms_defaults_gsis_us_east_1 = {}
dynamodb_cw_alarms_defaults_tables_us_west_2 = {
  "argus-connections"    = { table_name = "argus-connections" },
  "argus-messages"       = { table_name = "argus-messages" },
  "eyecue-helm-values"   = { table_name = "eyecue-helm-values" },
  "eyecue-things-shadow" = { table_name = "eyecue-things-shadow" },
  "eyecue-weights"       = { table_name = "eyecue-weights" },
}
dynamodb_cw_alarms_defaults_gsis_us_west_2 = {}
