from typing import Callable, List, Optional, Sequence, Tuple, Union
import base64
import ssl
from loguru import logger
from urllib.error import URLError
from urllib.request import <PERSON><PERSON><PERSON><PERSON>, build_opener, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPSHandler, Request
from prometheus_client import CollectorRegistry, Gauge
from prometheus_client.exposition import generate_latest
from prometheus_client.exposition import CONTENT_TYPE_LATEST


class VictoriaMetricInstance:
    def __init__(self, metrics_endpoint: str, username: str, password: str, timeout: int = 15):
        self.metrics_endpoint = metrics_endpoint
        self.username = username
        self.password = password
        self.timeout  = timeout

    def test_publish(self):
        registry = CollectorRegistry()
        g = Gauge('my_test_metric', 'This is a test metric', registry=registry)
        g.set(123.45)
        self._push(generate_latest(registry))

    def _push(self, observation: bytes):
        if self.metrics_endpoint is None:
            return

        auth_string = f"{self.username}:{self.password}"
        encoded_auth = base64.b64encode(auth_string.encode('utf-8')).decode('utf-8')

        try:
            self._default_handler(
                url=f"{self.metrics_endpoint}/api/v1/import/prometheus",
                method="POST",
                timeout=self.timeout,
                headers=[("Content-Type", CONTENT_TYPE_LATEST), ('Authorization', f'Basic {encoded_auth}')],
                data=observation,
            )()
            logger.trace(f"Metrics pushed successfully - {observation.decode('utf-8')}")
        except URLError as e:
            logger.error(f"VictoriaMetrics is not available: {e.reason}")
        except Exception as e:
            logger.exception(f"Unknown error while pushing to VM {e}")

    def _default_handler(
            self,
            url: str,
            method: str,
            timeout: Optional[float],
            headers: List[Tuple[str, str]],
            data: bytes,
    ) -> Callable[[], None]:
        """Default handler that implements HTTP/HTTPS connections.

        Used by the push_to_gateway functions. Can be re-used by other handlers."""
        if url.startswith("https://"):
            handler = HTTPSHandler(context=ssl._create_unverified_context())
        else:
            handler = HTTPHandler
        return self._make_handler(url, method, timeout, headers, data, handler)

    def _make_handler(
            self,
            url: str,
            method: str,
            timeout: Optional[float],
            headers: Sequence[Tuple[str, str]],
            data: bytes,
            base_handler: Union[BaseHandler, type],
    ) -> Callable[[], None]:
        def handle() -> None:
            request = Request(url, data=data)
            request.get_method = lambda: method  # type: ignore
            for k, v in headers:
                request.add_header(k, v)
            resp = build_opener(base_handler).open(request, timeout=timeout)
            if resp.code >= 400:
                raise OSError(f"error talking to pushgateway: {resp.code} {resp.msg}")

        return handle


if __name__ == "__main__":
    old_vm = VictoriaMetricInstance(
        metrics_endpoint="https://ec2-18-208-145-252.compute-1.amazonaws.com:8427",
        username="local-single-node",
        password="my-houst"
    )

    old_vm.test_publish()

    new_vm = VictoriaMetricInstance(
        metrics_endpoint="https://victoria-metrics.ap-southeast-2.fingermark.tech/insert/0/prometheus",
        username="ingester",
        password="YAXHrbCRg43iPpEJTNbw"
    )

    new_vm.test_publish()
