# ==========================================
# IAM Assume Role
# ==========================================

module "iam_password_policy" {
  source = "../../../../modules/iam_password_policy"
}

module "assume_role" {
  source = "../../../../modules/fingermark_users_assume_role"
  roles  = ["AdminAccess", "PowerAccess", "DeployerAccess", "DevAccess"]
}

# ==========================================
# VPC
# ==========================================

module "eyecue_network" {
  source                 = "../../../../modules/network"
  vpc_cidr_block         = var.vpc_cidr_block
  vpc_name               = "${var.customer}_${var.product}_${var.env}_${var.AWS_REGION}_vpc"
  azs                    = var.vpc_azs
  vpc_tags               = merge(var.default_tags, var.vpc_tags)
  public_subnets         = var.public_subnets
  private_subnets        = var.private_subnets
  havelocknorthaccess_sg = "enabled"
  tags                   = merge(var.default_tags, var.tags, { Stack = "network" })
}

module "vpc_flow_logs" {
  source          = "../../../../modules/vpc_flow_logs"
  log_destination = "arn:aws:s3:::fingermark-vpc-logs"
  tags            = var.tags
}

# ==========================================
# EYECUE Dashboard
# ==========================================

# ======== Domains ========

# ------- API Gateway --------

module "heartbeat_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "stg.api.eyecue-heartbeat-service.eyecue.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "EDGE"
  tags               = var.tags
  providers = {
    aws           = aws
    aws.us_east_1 = aws.us-east-1 # Must use us-east-1 for Edge-optimized API Gateway
  }
}

module "eyecue_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "stg.api.eyecuedataboard.com"
  cloudflare_zone_id = "aa00a0cdc808160d406c732989eddbca"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

module "heimdallr_api_gateway_domain_cloudflare" {
  source             = "../../../../modules/api_gateway_domain_cloudflare"
  domain_name        = "stg.eds.invoke-heimdallr.fingermarkai.tech"
  cloudflare_zone_id = "e8428c969a5cc8497a9f8684baba52de"
  endpoint_type      = "REGIONAL"
  tags               = var.tags

  providers = { aws.us_east_1 = aws, aws = aws } # Hack around non optional provider
}

# ------- Cloudflare DNS --------

module "cloudflare_dns_record" {
  source                  = "../../../../modules/cloudflare"
  cloudflare_zone_id      = "e8428c969a5cc8497a9f8684baba52de"
  cloudflare_record_name  = "stg.api.cjs.eyecue.fingermarkai.tech"
  cloudflare_record_value = "d-dhk8wmjlq3.execute-api.ap-southeast-2.amazonaws.com"
  cloudflare_api_key      = data.vault_generic_secret.cloudflare.data["api_key"]
  cloudflare_record_type  = "CNAME"
}

# ------- ACM Certificates -------

resource "aws_acm_certificate" "portal-eyecuedashboard" {
  provider          = aws.us-east-1
  domain_name       = "stg.portal.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "dashboard-eyecuedashboard" {
  provider          = aws.us-east-1
  domain_name       = "stg.dashboard.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "admin-eyecuedashboard" {
  provider          = aws.us-east-1
  domain_name       = "stg.admin.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_acm_certificate" "monitor-eyecuedashboard" {
  provider          = aws.us-east-1
  domain_name       = "stg.monitorv2.eyecuedashboard.com"
  validation_method = "DNS"
  tags              = merge(var.default_tags, var.tags)
  lifecycle {
    create_before_destroy = true
  }
}

# ======== DynamoDB Tables ========

module "eyecue_dashboard_dynamodb_tables" {
  source                 = "../../../../modules/eyecue_dashboard_dynamodb"
  dynamodb_tables_config = var.dynamodb_tables_config
}

# ======== Events ========

module "eyecue_sns_topic" {
  source     = "../../../../modules/eyecue_sns"
  sns_topics = var.sns_topics
}

module "eyecue_event_handler" {
  source         = "../../../../modules/eyecue_event_handler"
  environment    = var.ENVIRONMENT
  aws_region     = var.AWS_REGION
  aws_account_id = data.aws_caller_identity.current.account_id
}

module "eyecue_notification_service" {
  source         = "../../../../modules/eyecue_notification_service"
  environment    = var.ENVIRONMENT
  aws_account_id = data.aws_caller_identity.current.account_id
  aws_region     = var.AWS_REGION
}

# ======== S3 CloudFront and CORS ========

# -------- Portal Site --------

module "portal_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "stg-portal-eyecuedashboard"
  domain_name        = "stg.portal.eyecuedashboard.com"
  cloudfront_aliases = ["stg.portal.eyecuedashboard.com"]
  cloudfront_acm_arn = aws_acm_certificate.portal-eyecuedashboard.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

# -------- Admin Site --------

module "admin_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "stg-admin-eyecuedashboard"
  domain_name        = "stg.admin.eyecuedashboard.com"
  cloudfront_aliases = ["stg.admin.eyecuedashboard.com"]
  cloudfront_acm_arn = aws_acm_certificate.admin-eyecuedashboard.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "admin_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.admin_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://stg.portal.eyecuedashboard.com"]
}

# -------- Dashboard Site --------

module "dashboard_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "stg-dashboard-eyecuedashboard"
  domain_name        = "stg.dashboard.eyecuedashboard.com"
  cloudfront_aliases = ["stg.dashboard.eyecuedashboard.com"]
  cloudfront_acm_arn = aws_acm_certificate.dashboard-eyecuedashboard.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "dashboard_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.dashboard_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://stg.portal.eyecuedashboard.com"]
}

# -------- Monitor Site --------

module "monitor_s3_cloudfront" {
  source             = "../../../../modules/s3_cloudfront"
  s3_bucket_name     = "stg-monitorv2-eyecuedashboard"
  domain_name        = "stg.monitorv2.eyecuedashboard.com"
  cloudfront_aliases = ["stg.monitorv2.eyecuedashboard.com"]
  cloudfront_acm_arn = aws_acm_certificate.monitor-eyecuedashboard.arn
  tags               = merge(var.default_tags, { Stack = "Eyecue" })
  s3_website = {
    index_document = "index.html"
    error_document = "error.html"
  }
  error_response_cachettl     = "300"
  error_response_errorcode    = "403"
  error_response_responsecode = "200"
  error_response_pagepath     = "/index.html"
  block_public_acls           = true
  block_public_policy         = true
  ignore_public_acls          = true
  restrict_public_buckets     = true
}

module "monitor_s3_cors" {
  source                  = "../../../../modules/s3_cors_configuration"
  bucket_id               = module.monitor_s3_cloudfront.s3_bucket_id
  s3_cors_allowed_origins = ["https://stg.portal.eyecuedashboard.com"]
}

# ==========================================
# SOC2 Security
# ==========================================

# ------- Vanta / Compliance Detection -------

module "vanta" {
  source = "../../../../modules/vanta"
}

# ------- CloudWatch Log Retention Management -------

module "cw_log_retention_ap_southeast_2" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
}

module "cw_log_retention_us_east_1" {
  source         = "../../../../modules/cw_log_retention"
  retention_days = 365
  tags           = var.tags
  default_tags   = var.default_tags
  providers = {
    aws = aws.us-east-1
  }
}

# -------- AWS Config Recorder -------

module "aws_config_recorder" {
  source = "../../../../modules/aws_config_recorder"

  recorder_name      = "config-recorder"
  enable_recording   = true
  recording_strategy = "INCLUSION_BY_RESOURCE_TYPES"
  include_resource_types = [
    "AWS::IAM::Group",
    "AWS::IAM::Policy",
    "AWS::IAM::Role",
    "AWS::IAM::User",
  ]
  recording_frequency = "CONTINUOUS"

  # Re-use Control Tower deployed S3 Bucket and SNS topic
  s3_bucket_name = "aws-controltower-logs-************-ap-southeast-2"                               # Log Archive account
  s3_key_prefix  = "o-aydhjv9alg"                                                                    # Organization ID
  sns_topic_arn  = "arn:aws:sns:ap-southeast-2:************:aws-controltower-AllConfigNotifications" # Audit account

  tags = var.tags
}

# ==========================================
# CloudWatch Alarms
# ==========================================

locals {
  dynamodb_cw_alarms = {
    defaults = {
      ap_southeast_2 = {
        tables_config = merge(
          # Terraform'd DDB tables
          { for k, v in module.eyecue_dashboard_dynamodb_tables.dynamodb_tables_ids : v => { table_name = v } },
          # Additional DDB tables
          var.dynamodb_cw_alarms_defaults_tables_ap_southeast_2,
        )
        gsis_config = merge(
          # Terraform'd DDB GSIs
          { for v in module.eyecue_dashboard_dynamodb_tables.dynamodb_gsi_names_flattened_list : "${v.table_name}-${v.index_name}" => {
            table_name = v.table_name, index_name = v.index_name
          } },
          # Additional DDB GSIs
          var.dynamodb_cw_alarms_defaults_gsis_ap_southeast_2
        )
      }
    }
  }
}

module "dynamodb_cw_alarms_defaults_ap_southeast_2" {
  source                                 = "../../../../modules/dynamodb_cw_alarms"
  sns_topic_arns                         = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  cw_alarm_config_ddb_table_consumed_rcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_rcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config
  cw_alarm_config_ddb_table_consumed_wcu = local.dynamodb_cw_alarms.defaults.ap_southeast_2.tables_config
  cw_alarm_config_ddb_gsi_consumed_wcu   = local.dynamodb_cw_alarms.defaults.ap_southeast_2.gsis_config

  tags         = var.tags
  default_tags = var.default_tags
}

# Lambda global error rate monitoring for SOC2 compliance
module "lambda_error_monitoring_ap_southeast_2" {
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["ap-southeast-2"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Staging"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

# Lambda global error rate monitoring for us-east-1 region
module "lambda_error_monitoring_us_east_1" {
  providers = {
    aws = aws.us-east-1
  }
  source = "../../../../modules/lambda_error_monitoring"

  alarm_name              = "SOC2-GlobalLambdaErrorRate"
  alarm_description       = "SOC2 compliance - Monitors the global Lambda error rate across all functions"
  error_threshold_percent = 10 # Alarm when error rate exceeds 10%
  evaluation_periods      = 2  # Require breach for 2 consecutive periods
  period_seconds          = 3600

  sns_topic_arns      = [var.cw_alarms_sns_topic_arns_region_lookup["us-east-1"]]
  enable_notification = true

  tags = merge(var.tags, {
    Compliance  = "SOC2"
    Purpose     = "ErrorMonitoring"
    Environment = "Staging"
    Squad       = "Platform team"
  })
  default_tags = var.default_tags
}

resource "aws_s3_account_public_access_block" "block_public_access" {
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
